import DeviceInfo from 'react-native-device-info';

import {BlockbookProxyInstance} from '@/services/BackendServices';
import {store} from '@/storage/store';

export const getBlockbookHealthStatus = async () => {
  const response = await BlockbookProxyInstance.get('/v1/status/blockbook');
  return response.data;
};

/* ============================================================================================== */
/*                                       CLIENT ATTESTATION                                       */
/* ============================================================================================== */

export const createClientAttestation = async () => {};

/* ============================================================================================== */
/*                                         CLIENT SESSION                                         */
/* ============================================================================================== */

export const createClientSession = async () => {
  const response = await BlockbookProxyInstance.post('/auth/session', {
    deviceId: DeviceInfo.getUniqueId(),
    tenantId: 'corp_xyz',
    attestation: 'test', // TEMP not in use
    nonce: 'test123', // TEMP not in use
  });
  return response.data;
};

export const refreshClientSession = async () => {
  const response = await BlockbookProxyInstance.post('/auth/refresh');
  return response.data;
};

/* ============================================================================================== */
/*                                         WATCH ADDRESSES                                        */
/* ============================================================================================== */

export const watchClientAddresses = async () => {
  const response = await BlockbookProxyInstance.post('/v1/watch', {
    chain: 'BTC',
    addresses: [store.getState().auth.userAddresses[0].address],
  });

  return response.data;
};

export const unwatchClientAddresses = async () => {};

/* ============================================================================================== */
/*                                         DATA SNAPSHOTS                                         */
/* ============================================================================================== */

export const getBalances = async () => {
  const response = await BlockbookProxyInstance.get('/v1/snapshots/balances');
  return response.data;
};

export const getLightTransactions = async () => {
  const response = await BlockbookProxyInstance.get(
    '/v1/snapshots/txs?since=1716400000&level=light&page=1&pageSize=100',
  );
  return response.data;
};

export const getEVMTokenBalances = async () => {
  const response = await BlockbookProxyInstance.get(
    '/v1/snapshots/tokens?since=1716400000',
  );
  return response.data;
};
