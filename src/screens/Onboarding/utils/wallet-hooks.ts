import {useCallback, useState} from 'react';

import {useAppDispatch} from '@/hooks/redux';
import {setIsLoggedIn, setUser, setUserAddresses} from '@/storage/actions/authActions';
import {createClientSession} from '@/utils/blockbook/blockbook-actions';
import {WalletImportOptions, WalletImportResult} from './types';
import {importWalletFromMnemonic} from './wallet-helpers';

export const useWalletImport = () => {
  const dispatch = useAppDispatch();

  const [state, setState] = useState<{
    isLoading: boolean;
    error: 'string' | null;
    result: WalletImportResult | null;
  }>({
    isLoading: false,
    error: null,
    result: null,
  });

  const importWallet = useCallback(
    async (mnemonic: string, options?: WalletImportOptions): Promise<void> => {
      setState((prev) => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      try {
        const result = await importWalletFromMnemonic(mnemonic, options);

        dispatch(setUserAddresses(result.addresses));
        dispatch(
          setUser({
            wallet: result.wallets,
            pinCode: '',
          }),
        );

        // TODO::
        const session = await createClientSession();
        console.log('✅ Client Session Created >>> >>>', session);

        dispatch(setIsLoggedIn());

        setState((prev) => ({
          ...prev,
          isLoading: false,
          result,
        }));
      } catch (error) {
        console.error('An unexpected error occurred during wallet import');
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error,
        }));
        throw error;
      }
    },
    [dispatch],
  );

  const clearError = useCallback(() => {
    setState((prev) => ({
      ...prev,
      error: null,
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      result: null,
    });
  }, []);

  return {
    ...state,
    importWallet,
    clearError,
    reset,
  };
};
