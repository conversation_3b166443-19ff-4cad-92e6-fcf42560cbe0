import {NativeModules} from 'react-native';

import {ChainsOrder} from '@/constants/Chains';
import {WalletLoggerInstance} from '@/services/BackendServices';
import WalletService from '@/services/WalletService';
import {
  AuthAddress,
  AuthAddresses,
  AuthUserWallet,
  NativeUserWallet,
} from '@/types/authTypes';
import {enableNotifications} from '@/utils/index';
import {WalletImportOptions, WalletImportResult} from './types';

const {WalletManagerBridge} = NativeModules;
const {Enumerations, Services} = require('@AssetifyNet/cryptoapis-kms');

/**
 * Imports a wallet from a mnemonic phrase. Handles both existing and new wallet scenarios
 */
export const importWalletFromMnemonic = async (
  mnemonic: string,
  options: WalletImportOptions = {},
): Promise<WalletImportResult> => {
  const {enableNotificationsOnSuccess = true, walletService = new WalletService()} =
    options;

  try {
    // Validate mnemonic format
    const mnemonicWords = mnemonic.trim().toLowerCase().split(/\s+/);
    if (mnemonicWords.length !== 12) {
      throw new Error('Invalid mnemonic: must contain exactly 12 words');
    }

    const normalizedMnemonic = mnemonicWords.join(' ');

    // Step 1: Get Bitcoin wallet to check if wallet exists
    const {wallet: bitcoinWallet, address: bitcoinAddress} = await getBitcoinWallet(
      normalizedMnemonic,
    );

    if (!bitcoinWallet.xPubsList?.[0]?.accountXpub) {
      throw new Error('Failed to generate Bitcoin wallet: missing xPub data');
    }

    // Step 2: Check if wallet already exists in the system
    const bitcoinChains = await walletService.walletLoggerBTC(
      bitcoinAddress.address,
      bitcoinWallet.xPubsList[0].accountXpub,
    );

    let wallets: AuthUserWallet[];
    let addresses: AuthAddresses;
    let isExistingWallet: boolean;

    if (bitcoinChains.status === 409) {
      // Wallet exists - restore from existing data
      isExistingWallet = true;
      const existingWalletData = await getExistingWallets(bitcoinWallet, bitcoinAddress);
      wallets = existingWalletData.wallets;
      addresses = existingWalletData.addresses;
    } else {
      // New wallet - create all wallets and addresses
      isExistingWallet = false;

      const createdWallets = await walletService.createWallets(normalizedMnemonic);
      if (!createdWallets) {
        throw new Error('Failed to create wallets from mnemonic');
      }

      const createdAddresses = await walletService.createAddresses(createdWallets);
      if (!createdAddresses) {
        throw new Error('Failed to create addresses from wallets');
      }

      const {
        wallets: processedWallets,
        addresses: processedAddresses,
        loggerRequest,
      } = await walletService.createLoggerRequest(
        createdWallets,
        createdAddresses as AuthAddresses,
      );

      // Register the new wallet with the backend
      await walletService.walletLogger(loggerRequest);

      wallets = processedWallets;
      addresses = processedAddresses;
    }

    // Step 3: Enable notifications if requested and we have a valid address
    if (enableNotificationsOnSuccess && addresses.length > 0) {
      const firstAddress = addresses[0] as AuthAddress | undefined;
      if (firstAddress && 'address' in firstAddress && firstAddress.address) {
        try {
          await enableNotifications(firstAddress.address);
        } catch (notificationError) {
          // Log but don't fail the import for notification errors
          console.warn('Failed to enable notifications:', notificationError);
        }
      }
    }

    return {
      wallets,
      addresses,
      isExistingWallet,
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Validates a mnemonic phrase format
 */
export const validateMnemonicFormat = (mnemonic: string): boolean => {
  if (!mnemonic || typeof mnemonic !== 'string') {
    return false;
  }

  const words = mnemonic.trim().split(/\s+/);
  return words.length === 12 && words.every((word) => word.length > 0);
};

const parseChains = (chain: string) => {
  switch (chain) {
    case 'btc':
      return 'bitcoin';
    case 'eth':
      return 'ethereum';
    case 'bsc':
      return 'binance-smart-chain';
    case 'tron':
      return 'trx';
    case 'btc_cash':
      return 'bitcoin-cash';
    case 'ltc':
      return 'litecoin';
    case 'doge':
      return 'dogecoin';
    case 'avax':
      return 'avalanche';
    case 'avalanche':
      return 'avalanche';
    default:
      return chain;
  }
};

export const getBitcoinWallet = async (mnemonic: string) => {
  const wallet: NativeUserWallet = await WalletManagerBridge.createHDWalletFromMnemonic(
    mnemonic,
    Enumerations.Blockchains.BITCOIN,
  );
  const address: AuthAddress = {
    address: wallet.address!,
    privateKey: wallet.privateKey!,
    publicKey: wallet.publicKey!,
    chain: wallet.blockchain,
  };

  return {wallet, address};
};

const fetchExistingWallet = (address: string) =>
  WalletLoggerInstance.get(`/v2/wallet/xpub-data/${address}/mainnet`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  })
    .then((response) => response.data)
    .catch((error) => {
      console.log('fetchExistingWallet', error);
      throw error;
    });

export const getExistingWallets = async (
  bitcoinWallet: NativeUserWallet,
  address: any,
) => {
  let wallets = [];
  let addresses = [];

  const existingWallets = await fetchExistingWallet(address.address);

  let kaspaAddresses: AuthAddress[] = [];

  const addressService = new Services.AddressService(
    Enumerations.Blockchains.KASPA,
    'mainnet',
  );

  const walletService = new Services.WalletService(
    Enumerations.Blockchains.KASPA,
    'mainnet',
  );

  const kaspaHDWallet = await walletService.createHDWalletFromMnemonic(
    bitcoinWallet?.mnemonic,
  );

  for (let i = 0; i < 10; i++) {
    const address = await addressService.generateAddressFromHDWalletWithCustomPath(
      kaspaHDWallet,
      `/0/${i}`,
    );

    kaspaAddresses.push(address._data === undefined ? address : address._data);
  }

  console.log('existingWallets', existingWallets);

  const mnemonic = bitcoinWallet.mnemonic;

  for (let i = 0; i < existingWallets.chains.length; i++) {
    const wallet = existingWallets.chains[i];

    const createdWallet = await WalletManagerBridge.createHDWalletFromMnemonic(
      mnemonic,
      parseChains(wallet.chain),
    );

    let index = ChainsOrder.indexOf(parseChains(wallet.chain));

    const address = {
      address: createdWallet.address.includes('bitcoincash:')
        ? createdWallet.address.replace('bitcoincash:', '')
        : createdWallet.address,
      privateKey: createdWallet.privateKey,
      publicKey: createdWallet.publicKey,
      chain: createdWallet.blockchain,
    };

    console.log('address', address);
    console.log('wallet', createdWallet);

    wallets[index] = {
      seed: createdWallet.seed,
      zPub: createdWallet.zPub ? createdWallet.zPub : createdWallet.xPubsList[0],
      mnemonic: createdWallet.mnemonic,
      blockchain: createdWallet.blockchain,
      network: createdWallet.network,
    };
    addresses[index] =
      createdWallet.blockchain !== 'kaspa'
        ? address
        : {
            addresses: kaspaAddresses,
            chain: Enumerations.Blockchains.KASPA,
          };
  }

  return {wallets, addresses};
};

/* ============================================================================================== */
/*                                         TEMP:: KEYCHAIN                                        */
/* ============================================================================================== */

export const stripServicePrefix = (service: string): string => {
  const prefixes = ['assetify-password_', 'assetify-recovery_'];

  for (const prefix of prefixes) {
    if (service.startsWith(prefix)) {
      return service.substring(prefix.length);
    }
  }

  return service;
};
