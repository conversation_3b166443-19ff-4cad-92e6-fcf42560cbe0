import {memo, useCallback, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {KeyboardAvoidingView, Platform, StyleSheet, TextInput, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import GlobalStyles from '@/constants/GlobalStyles';
import {useFocusInput} from '@/hooks/keyboard';
import theme from '@/styles/theme';
import {getKeychainValue} from '@/utils/keychain';
import {showWarningToast} from '@/utils/toast';
import MButton from '@components/MButton';
import {PasswordInput} from '@components/PasswordInput';
import {Caption, Footer, Subheading} from '@styles/styled-components';
import {validateMnemonicFormat} from '../../utils/wallet-helpers';
import {useWalletImport} from '../../utils/wallet-hooks';

const RestoreRecoveryAndImport = ({route}: {route: any}) => {
  const recoveryId = route.params?.recoveryId;

  const {t} = useTranslation();
  const inputRef = useRef<TextInput>(null);

  useFocusInput(inputRef);

  const {importWallet, isLoading} = useWalletImport();
  const [password, setPassword] = useState('');

  const handleRestoreWallet = useCallback(async () => {
    if (!recoveryId) return;

    try {
      const result = await getKeychainValue(recoveryId, password);

      if (!result) {
        showWarningToast('Incorrect Password');
        return;
      }

      const seedPhrase = result.password;

      if (!seedPhrase) {
        console.error('[RestoreRecoveryAndImport] No seed phrase found');
        showWarningToast('There was an error restoring your wallet. Please try again');
        return;
      }

      if (!validateMnemonicFormat(seedPhrase)) {
        console.error('[RestoreRecoveryAndImport] Invalid seed phrase format');
        showWarningToast('Invalid recovery phrase format. Please try again');
        return;
      }

      await importWallet(seedPhrase);
    } catch (error) {
      console.error('[RestoreRecoveryAndImport] Error restoring wallet:', error);
      showWarningToast('There was an error restoring your wallet. Please try again');
    }
  }, [recoveryId, password, importWallet]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? (theme.isSmallDevice ? 60 : 90) : 0}
      style={styles.keyboardAvoidingView}
    >
      <KeyboardAwareScrollView
        style={styles.scrollView}
        keyboardShouldPersistTaps="handled"
        enableAutomaticScroll={false}
        enableOnAndroid
      >
        <View style={styles.content}>
          <Caption style={styles.caption}>{t('wallet.restoreWallet')}</Caption>

          <Subheading style={styles.description}>
            {t('wallet.restoreWalletAndImport')}
          </Subheading>

          <PasswordInput
            ref={inputRef}
            label="Password"
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
          />
        </View>
      </KeyboardAwareScrollView>

      <Footer style={styles.footer}>
        <MButton
          text={t('wallet.restoreWallet')}
          onPress={handleRestoreWallet}
          disabled={!password || isLoading}
          isLoading={isLoading}
          loadingText="Restoring Wallet"
        />
      </Footer>
    </KeyboardAvoidingView>
  );
};

export default memo(RestoreRecoveryAndImport);

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen,
    paddingVertical: theme.layout.pv.screen * 2,
    justifyContent: 'flex-start',
    gap: theme.spacing.lg,
  },
  caption: {
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
  },
  footer: {
    paddingHorizontal: theme.layout.ph.screen * 2,
  },
});
