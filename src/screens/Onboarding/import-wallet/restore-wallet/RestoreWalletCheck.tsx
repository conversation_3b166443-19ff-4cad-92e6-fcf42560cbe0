import {useFocusEffect} from '@react-navigation/native';
import {memo, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import {ArrowDownTrayIcon} from 'react-native-heroicons/outline';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import GlobalStyles from '@/constants/GlobalStyles';
import {Caption} from '@styles/styled-components';
import theme from '@styles/theme';
import {getAllKeychainRecoveryServices} from '../../../KeychainBackup/helpers-keychain';
import {stripServicePrefix} from '../../utils/wallet-helpers';

const RestoreWalletCheck = ({navigation}: {navigation: any}) => {
  const {t} = useTranslation();

  useFocusEffect(
    useCallback(() => {
      let isSubscribed = true;

      const checkSecureValue = async () => {
        try {
          const keyichainRecoveryServices = await getAllKeychainRecoveryServices();

          const phraseItems = keyichainRecoveryServices.map((service) => {
            const parts = service.split('__');

            const walletLabel = stripServicePrefix(parts[0] || '');

            return {
              service,
              displayName: walletLabel,
            };
          });

          await new Promise((resolve) => setTimeout(resolve, 2_000));

          if (isSubscribed) {
            if (!keyichainRecoveryServices || keyichainRecoveryServices.length === 0) {
              navigation.replace('RestoreWalletListAvailable', {
                recoveryPhrases: [],
                loading: false,
              });
            } else {
              navigation.replace('RestoreWalletListAvailable', {
                recoveryPhrases: phraseItems,
                loading: false,
              });
            }
          }
        } catch (error) {
          console.log('[RestoreWalletCheck] Error checking secure value:', error);
          navigation.goBack();
        }
      };

      checkSecureValue();

      return () => {
        isSubscribed = false;
      };
    }, [navigation]),
  );

  return (
    <View style={styles.content}>
      <View style={styles.iconContainer}>
        <ArrowDownTrayIcon size={32} stroke={theme.colors.base.black} />
      </View>

      <Caption style={styles.searchingText}>{t('wallet.restoreWalletCheck')}</Caption>

      <View style={styles.skeletonContainer}>
        {[1, 2, 3].map((index) => (
          <View key={index} style={styles.skeletonItemContainer}>
            <SkeletonPlaceholder speed={1_500}>
              <SkeletonPlaceholder.Item
                flexDirection="row"
                alignItems="center"
                justifyContent="center"
                padding={12}
              >
                <SkeletonPlaceholder.Item width={40} height={40} borderRadius={20} />

                <SkeletonPlaceholder.Item
                  width="70%"
                  height={16}
                  borderRadius={8}
                  marginLeft={12}
                />
              </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
          </View>
        ))}
      </View>
    </View>
  );
};

export default memo(RestoreWalletCheck);

const styles = StyleSheet.create({
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.sm,
    paddingTop: theme.spacing.xl,
    gap: theme.spacing.xxl,
    backgroundColor: theme.colors.light.background,
  },
  iconContainer: {
    width: 58,
    height: 58,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    borderRadius: theme.layout.borderRadius.sm,
    backgroundColor: GlobalStyles.gray.gray500,
  },
  searchingText: {
    textAlign: 'center',
  },
  skeletonContainer: {
    gap: theme.spacing.sm,
  },
  skeletonItemContainer: {
    paddingVertical: 8,
    borderColor: GlobalStyles.gray.gray500,
    borderWidth: 1,
    borderRadius: 12,
  },
  loadingTitle: {
    textAlign: 'center',
  },
  title: {
    textAlign: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
});
