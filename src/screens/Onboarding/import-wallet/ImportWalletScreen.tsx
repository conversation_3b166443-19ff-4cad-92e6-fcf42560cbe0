import analytics from '@react-native-firebase/analytics';
import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useRef, useState} from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  TextInput as RNTextInput,
  StyleSheet,
  View,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import GlobalStyles from '@/constants/GlobalStyles';
import {useFocusInput} from '@/hooks/keyboard';
import WalletLoader from '@/screens/Onboarding/components/WalletLoader';
import {validateMnemonicFormat} from '@/screens/Onboarding/utils/wallet-helpers';
import MButton from '@components/MButton';
import {BodyM, Caption, Footer} from '@styles/styled-components';
import theme from '@styles/theme';
import {useWalletImport} from '../utils/wallet-hooks';
import InputMnemonic from './components/InputMnemonic';

const ImportWalletScreen = ({navigation}: any) => {
  const inputRef = useRef<RNTextInput>(null);
  useFocusInput(inputRef);
  const {importWallet, isLoading} = useWalletImport();

  const [mnemonic, setMnemonic] = useState('');

  const handleMnemonicsSubmit = (text: string) => {
    const mnemonicsArray = text.trim().toLowerCase().split(/\s+/);
    setMnemonic(mnemonicsArray.join(' '));
  };

  const handleMnemonics = useCallback((text: string) => {
    const mnemonicsArray = text.toLowerCase().split(/\s+/);

    if (mnemonicsArray.length > 12) {
      mnemonicsArray.length = 12;
    }

    setMnemonic(mnemonicsArray.join(' '));
  }, []);

  const handleContinue = useCallback(async () => {
    if (!validateMnemonicFormat(mnemonic)) {
      return;
    }

    navigation.setParams({loading: true});

    try {
      await importWallet(mnemonic);
    } catch (error) {
      console.error('[ImportWalletScreen] Import failed:', error);
      navigation.setParams({loading: false});
    }
  }, [mnemonic, navigation, importWallet]);

  useFocusEffect(
    useCallback(() => {
      analytics().logEvent('ImportWallet_screen_open');
      setMnemonic('');
      navigation.setParams({loading: false});
    }, [navigation]),
  );

  if (isLoading) {
    return <WalletLoader isWalletBeingCreated={false} />;
  }

  const isValidMnemonic = validateMnemonicFormat(mnemonic);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? (theme.isSmallDevice ? 60 : 90) : 0}
      style={styles.keyboardAvoidingView}
    >
      <KeyboardAwareScrollView
        style={styles.scrollView}
        enableAutomaticScroll={false}
        enableOnAndroid
      >
        <View style={styles.container}>
          <View style={styles.content}>
            <Caption style={styles.caption}>Enter your recovery phrase</Caption>

            <BodyM style={styles.subHeaderText}>
              Your recovery phrase will only be stored locally on your device
            </BodyM>

            <InputMnemonic
              ref={inputRef}
              handleMnemonics={handleMnemonics}
              handleMnemonicsSubmit={handleMnemonicsSubmit}
              mnemonics={mnemonic}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>

      <Footer style={[styles.footer]}>
        <MButton
          text="Import Wallet"
          onPress={handleContinue}
          disabled={!isValidMnemonic || isLoading}
          isLoading={isLoading}
        />
      </Footer>
    </KeyboardAvoidingView>
  );
};

export default ImportWalletScreen;

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    paddingTop: theme.layout.pv.screen * 2,
    gap: theme.spacing.lg,
  },
  caption: {
    textAlign: 'center',
  },
  subHeaderText: {
    textAlign: 'center',
    paddingHorizontal: theme.spacing.xl,
    color: GlobalStyles.gray.gray900,
  },
  footer: {
    paddingHorizontal: theme.isSmallDevice
      ? theme.layout.ph.screen * 3
      : theme.layout.ph.screen * 2,
  },
});
