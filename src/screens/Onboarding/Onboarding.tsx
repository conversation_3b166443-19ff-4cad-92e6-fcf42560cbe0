import React, {memo, useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import Animated, {useAnimatedStyle, withTiming} from 'react-native-reanimated';

import {useSnapPointsTwo} from '@/hooks/bottomSheet';
import theme from '@/styles/theme';
import Logo from '@assets/logo/Logo.svg';
import BottomSheetWrapper from '@components/BottomSheetWrapper';
import CarouselNormal from '@components/CarouselNormal';
import MButton from '@components/MButton';
import SafeAreaInset from '@components/SafeAreaInset';
import {Footer, Headline, Subheading} from '@styles/styled-components';
import AgreePopUpDialog from './AgreePopUpDialog';
import {confirmSwitches, ONBOARDING_CAROUSEL_DATA, OnboardingSlide} from './utils';

const Onboarding: React.FC = ({navigation}: any) => {
  const snapPoints = useSnapPointsTwo(theme.isSmallDevice ? 'lg' : 'md');
  const {t} = useTranslation();

  const [sheetVisible, setSheetVisible] = useState<boolean>(false);
  const [goToScreen, setGoToScreen] = useState<any>({name: ''});
  const [currentSlide, setCurrentSlide] = useState(0);

  const handleSlideChange = useCallback((index: number) => {
    setCurrentSlide(index);
  }, []);

  const handleNewWalletPress = useCallback(() => {
    setSheetVisible(true);
    setGoToScreen({name: 'new-wallet'});
  }, []);

  const handleImportWalletPress = useCallback(() => {
    setSheetVisible(true);
    setGoToScreen({name: 'import-wallet'});
  }, []);

  const handleSheetContinuePress = useCallback(async () => {
    setSheetVisible(false);

    if (goToScreen.name === 'new-wallet') {
      navigation.navigate('Onboarding', {
        screen: 'NewWalletCreating',
      });
    }

    if (goToScreen.name === 'import-wallet') {
      navigation.navigate('Onboarding', {
        screen: 'ImportWalletOptions',
      });
    }
  }, [goToScreen, navigation]);

  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(currentSlide === 0 ? 0 : 1, {
        duration: 300,
      }),
    };
  }, [currentSlide]);

  const renderSlide = useCallback(
    (info: {item: unknown; index: number}) => {
      const {item, index} = info;

      const slideItem = item as OnboardingSlide;
      const isFirstSlide = index === 0;

      return (
        <View style={[styles.slide, {width: theme.SCREEN_WIDTH}]}>
          <View
            style={[
              styles.imageContainer,
              isFirstSlide ? styles.firstSlideImage : styles.otherSlideImage,
            ]}
          >
            <slideItem.image
              width={theme.layout.images.md}
              height={theme.layout.images.md}
            />
          </View>

          <View
            style={[
              styles.textContainer,
              isFirstSlide && styles.firstSlideTextContainer,
              !isFirstSlide && styles.centeredTextContainer,
            ]}
          >
            <Headline style={[!isFirstSlide && styles.centeredText]}>
              {slideItem.title}
            </Headline>

            <Subheading style={[!isFirstSlide && styles.centeredText]}>
              {slideItem.description}
            </Subheading>
          </View>
        </View>
      );
    },
    [theme.SCREEN_WIDTH],
  );

  return (
    <View style={styles.root}>
      <Animated.View style={[styles.headerContainer, headerAnimatedStyle]}>
        <SafeAreaInset type="top" />

        <View style={styles.headerContent}>
          <Logo width={theme.layout.images.xs} height={theme.layout.images.xs} />
        </View>
      </Animated.View>

      <View style={styles.carouselContainer}>
        <CarouselNormal
          data={ONBOARDING_CAROUSEL_DATA}
          renderItem={renderSlide}
          onSlideChange={handleSlideChange}
        />
      </View>

      <Footer style={styles.footer}>
        <MButton text={t('wallet.new_wallet')} onPress={handleNewWalletPress} />

        <MButton
          text={t('wallet.import_wallet')}
          variant="secondary"
          onPress={handleImportWalletPress}
        />
      </Footer>

      {sheetVisible && (
        <BottomSheetWrapper
          isOpen={sheetVisible}
          snapPoints={snapPoints}
          onClose={() => setSheetVisible(false)}
        >
          <AgreePopUpDialog
            onAction={handleSheetContinuePress}
            switches={confirmSwitches}
          />
        </BottomSheetWrapper>
      )}
    </View>
  );
};

export default memo(Onboarding);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: theme.colors.light.background,
  },
  headerContainer: {
    width: '100%',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: theme.spacing.md,
  },
  carouselContainer: {
    flex: 1,
    marginBottom: 4,
  },
  slide: {
    flex: 1,
    alignItems: 'flex-start',
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.xxxl,
  },
  imageContainer: {
    width: '100%',
    marginVertical: theme.spacing.md,
  },
  firstSlideImage: {
    alignItems: 'flex-start',
    marginTop: theme.isSmallDevice ? theme.spacing.xxl : theme.spacing.xxxl * 2,
  },
  firstSlideTextContainer: {
    alignItems: 'flex-start',
    marginTop: theme.spacing.sm,
  },
  centeredTextContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: theme.isSmallDevice ? theme.spacing.sm : theme.spacing.xl,
  },
  centeredText: {
    textAlign: 'center',
  },
  otherSlideImage: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    width: '100%',
    gap: theme.spacing.md,
  },
  footer: {
    gap: theme.isSmallDevice ? theme.spacing.sm : theme.spacing.md,
    paddingHorizontal: theme.spacing.xxxl,
  },
});
