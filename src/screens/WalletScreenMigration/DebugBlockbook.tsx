import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { getBlockbookHealthStatus, getBalances } from '@/utils/blockbook/blockbook-actions';
import { useAuth } from '@/hooks/redux';

const DebugBlockbook = () => {
  const { userAddresses } = useAuth();
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [balances, setBalances] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testHealthStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔵 Testing health status...');
      const health = await getBlockbookHealthStatus();
      console.log('🔵 Health status result:', health);
      setHealthStatus(health);
    } catch (err) {
      console.error('🔴 Health status error:', err);
      setError(err instanceof Error ? err.message : 'Health status failed');
    } finally {
      setLoading(false);
    }
  };

  const testBalances = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔵 Testing balances...');
      const balanceData = await getBalances();
      console.log('🔵 Balances result:', balanceData);
      setBalances(balanceData);
    } catch (err) {
      console.error('🔴 Balances error:', err);
      setError(err instanceof Error ? err.message : 'Balances failed');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔵 DebugBlockbook: userAddresses:', userAddresses);
  }, [userAddresses]);

  return (
    <ScrollView style={{ flex: 1, padding: 20 }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        Blockbook Debug
      </Text>
      
      <Text style={{ marginBottom: 10 }}>
        User Address: {userAddresses[0]?.address || 'No address'}
      </Text>

      <TouchableOpacity
        onPress={testHealthStatus}
        style={{
          backgroundColor: '#007AFF',
          padding: 15,
          borderRadius: 8,
          marginBottom: 10,
        }}
        disabled={loading}
      >
        <Text style={{ color: 'white', textAlign: 'center' }}>
          {loading ? 'Loading...' : 'Test Health Status'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={testBalances}
        style={{
          backgroundColor: '#34C759',
          padding: 15,
          borderRadius: 8,
          marginBottom: 20,
        }}
        disabled={loading}
      >
        <Text style={{ color: 'white', textAlign: 'center' }}>
          {loading ? 'Loading...' : 'Test Balances'}
        </Text>
      </TouchableOpacity>

      {error && (
        <View style={{ backgroundColor: '#FF3B30', padding: 10, borderRadius: 8, marginBottom: 20 }}>
          <Text style={{ color: 'white' }}>Error: {error}</Text>
        </View>
      )}

      {healthStatus && (
        <View style={{ marginBottom: 20 }}>
          <Text style={{ fontWeight: 'bold', marginBottom: 10 }}>Health Status:</Text>
          <Text style={{ fontFamily: 'monospace', fontSize: 12 }}>
            {JSON.stringify(healthStatus, null, 2)}
          </Text>
        </View>
      )}

      {balances && (
        <View style={{ marginBottom: 20 }}>
          <Text style={{ fontWeight: 'bold', marginBottom: 10 }}>Balances:</Text>
          <Text style={{ fontFamily: 'monospace', fontSize: 12 }}>
            {JSON.stringify(balances, null, 2)}
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

export default DebugBlockbook;
