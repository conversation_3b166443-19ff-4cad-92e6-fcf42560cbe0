import { ChainToTokens, StableChains } from '@/constants/Chains';
import { TrailingZeroRegex } from '@/utils/parsing';
import {
  BlockbookBalancesResponse,
  BlockbookChainBalance,
  BlockbookHealthResponse,
  BlockbookToken,
  CHAIN_CONFIGS,
  PriceData,
  SubscribedChain,
  TransformedAsset,
  TransformedToken,
} from '../types/blockbookTypes';

/**
 * Converts raw balance from smallest unit to human-readable format
 */
export const formatBalance = (
  rawBalance: string,
  decimals: number,
  maxDecimalPlaces: number = 8
): string => {
  const balance = parseFloat(rawBalance) / Math.pow(10, decimals);
  
  if (balance === 0) return '0';
  
  // Dynamic decimal places based on balance magnitude
  const integerPartLength = Math.floor(balance).toString().length;
  const decimalPlaces = Math.max(0, Math.min(maxDecimalPlaces, 8 - integerPartLength));
  
  let formattedBalance = balance.toFixed(decimalPlaces);
  
  // Remove trailing zeros
  const parts = formattedBalance.split('.');
  if (parts[1]) {
    parts[1] = parts[1].replace(TrailingZeroRegex, '');
    formattedBalance = parts[1] ? `${parts[0]}.${parts[1]}` : parts[0];
  }
  
  return formattedBalance;
};

/**
 * Calculates fiat value for an asset
 */
export const calculateFiatValue = (
  balance: string,
  pricePerUnit: string,
  currency: string = 'USD'
): string => {
  const balanceNum = parseFloat(balance);
  const priceNum = parseFloat(pricePerUnit);
  
  if (isNaN(balanceNum) || isNaN(priceNum)) return '0.00';
  
  const fiatValue = balanceNum * priceNum;
  return fiatValue.toFixed(2);
};

/**
 * Determines subscribed chains from health status and user addresses
 */
export const getSubscribedChains = (
  healthStatus: BlockbookHealthResponse,
  userAddress: string
): SubscribedChain[] => {
  const subscribedChains: SubscribedChain[] = [];
  
  Object.entries(healthStatus.status).forEach(([chainSymbol, status]) => {
    const chainConfig = CHAIN_CONFIGS[chainSymbol];
    if (!chainConfig) return;
    
    const isSubscribed = status.subscribedAddresses > 0;
    const hasUserAddress = status.addresses.includes(userAddress);
    const isConnected = status.connection === 'connected';
    
    // Only include chains that are connected, subscribed, and contain user's address
    if (isConnected && isSubscribed && hasUserAddress) {
      subscribedChains.push({
        chainSymbol,
        chainName: chainConfig.name,
        isSubscribed,
        isConnected,
        userAddress,
      });
    }
  });
  
  return subscribedChains;
};

/**
 * Transforms blockbook chain balance to UI asset format
 */
export const transformChainBalance = (
  chainBalance: BlockbookChainBalance,
  priceData: PriceData,
  currency: string = 'USD'
): TransformedAsset | null => {
  const chainConfig = CHAIN_CONFIGS[chainBalance.chain];
  if (!chainConfig) return null;
  
  const rawBalance = chainBalance.balance;
  const formattedBalance = formatBalance(rawBalance, chainConfig.decimals);
  
  // Skip if balance is zero
  if (parseFloat(formattedBalance) === 0) return null;
  
  const pricePerUnit = priceData[chainConfig.symbol] || '0';
  const calculatedValue = calculateFiatValue(formattedBalance, pricePerUnit, currency);
  
  return {
    title: chainConfig.name,
    amount: formattedBalance,
    tokenLogo: chainConfig.name,
    tokenSymbol: chainConfig.symbol,
    blockchain: chainConfig.name,
    network: 'mainnet',
    rawBalance,
    decimals: chainConfig.decimals,
    fiatPrice: pricePerUnit,
    calculatedValue,
  };
};

/**
 * Transforms blockbook tokens to UI token format
 */
export const transformTokens = (
  tokens: BlockbookToken[],
  chainName: string,
  priceData: PriceData,
  currency: string = 'USD'
): TransformedToken[] => {
  const transformedTokens: TransformedToken[] = [];
  
  // Get supported tokens for this chain
  const supportedTokens = ChainToTokens[chainName] || [];
  
  tokens.forEach((token) => {
    // Check if token is in supported list
    const isSupported = supportedTokens.some(
      (supportedToken) => supportedToken.tokenSymbol === token.symbol
    );
    
    if (!isSupported || !token.balance) return;
    
    const formattedBalance = formatBalance(token.balance, token.decimals);
    
    // Skip if balance is zero
    if (parseFloat(formattedBalance) === 0) return;
    
    const pricePerUnit = priceData[token.symbol] || '0';
    const calculatedValue = calculateFiatValue(formattedBalance, pricePerUnit, currency);
    
    transformedTokens.push({
      title: chainName,
      amount: formattedBalance,
      tokenLogo: token.symbol,
      tokenSymbol: token.symbol,
      blockchain: chainName,
      network: 'mainnet',
      rawBalance: token.balance,
      decimals: token.decimals,
      fiatPrice: pricePerUnit,
      calculatedValue,
      contractAddress: token.contract,
      tokenName: token.name,
      isToken: true,
    });
  });
  
  return transformedTokens;
};

/**
 * Transforms complete blockbook response to UI format
 */
export const transformBlockbookData = (
  balancesResponse: BlockbookBalancesResponse,
  subscribedChains: SubscribedChain[],
  priceData: PriceData,
  currency: string = 'USD'
): { assets: TransformedAsset[]; tokens: TransformedToken[]; totalBalance: string } => {
  const assets: TransformedAsset[] = [];
  const tokens: TransformedToken[] = [];
  let totalFiatValue = 0;
  
  // Filter balances to only include subscribed chains and user's address
  const subscribedChainSymbols = subscribedChains.map(chain => chain.chainSymbol);
  const userAddress = subscribedChains[0]?.userAddress;
  
  const relevantBalances = balancesResponse.balances.filter(
    (balance) => 
      subscribedChainSymbols.includes(balance.chain) && 
      balance.address === userAddress
  );
  
  relevantBalances.forEach((chainBalance) => {
    // Transform main chain asset
    const transformedAsset = transformChainBalance(chainBalance, priceData, currency);
    if (transformedAsset) {
      assets.push(transformedAsset);
      totalFiatValue += parseFloat(transformedAsset.calculatedValue || '0');
    }
    
    // Transform tokens if they exist
    if (chainBalance.tokens && chainBalance.tokens.length > 0) {
      const chainConfig = CHAIN_CONFIGS[chainBalance.chain];
      if (chainConfig) {
        const transformedTokens = transformTokens(
          chainBalance.tokens,
          chainConfig.name,
          priceData,
          currency
        );
        tokens.push(...transformedTokens);
        
        // Add token values to total
        transformedTokens.forEach((token) => {
          totalFiatValue += parseFloat(token.calculatedValue || '0');
        });
      }
    }
  });
  
  return {
    assets,
    tokens,
    totalBalance: totalFiatValue.toFixed(2),
  };
};

/**
 * Merges assets and tokens for display in AssetList
 */
export const mergeAssetsAndTokens = (
  assets: TransformedAsset[],
  tokens: TransformedToken[]
): TransformedAsset[] => {
  const merged = [...assets];
  
  // Group tokens by blockchain
  const tokensByBlockchain = tokens.reduce((acc, token) => {
    if (!acc[token.blockchain]) {
      acc[token.blockchain] = [];
    }
    acc[token.blockchain].push(token);
    return acc;
  }, {} as Record<string, TransformedToken[]>);
  
  // Add tokens to their respective blockchain assets
  merged.forEach((asset) => {
    const chainTokens = tokensByBlockchain[asset.blockchain];
    if (chainTokens) {
      (asset as any).tokens = chainTokens;
    }
  });
  
  return merged;
};
