import {LayoutAnimation} from 'react-native';

export const toggleStableCoins = (
  setExpand: (value: boolean) => void,
  expand: boolean,
) => {
  const config = {
    duration: 250,
    update: {
      duration: 250,
      property: LayoutAnimation.Properties.opacity,
      type: LayoutAnimation.Types.easeInEaseOut,
    },
    delete: {
      duration: 150,
      property: LayoutAnimation.Properties.opacity,
      type: LayoutAnimation.Types.easeInEaseOut,
    },
  };
  LayoutAnimation.configureNext(config);
  setExpand(!expand);
};
