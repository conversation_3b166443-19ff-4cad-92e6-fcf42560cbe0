import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {TouchableOpacity, View} from 'react-native';

import BlurView from '@/components/BlurView';
import {AuthAsset} from '@/types/authTypes';
import FixedText from '@/components/FixedText/FixedText';
import Logo from '@/components/Logo/AssetLogo';
import {ChainToTokens, StableChains} from '@/constants/Chains';
import {NameParsing, parsePrice, symbolToSvg} from '@/utils/parsing';
import {styles} from './styles';
import {toggleStableCoins} from './walletAssetUtils';

type WalletAssetProps = {
  asset: AuthAsset;
  calculatedPrices: string[];
  tokenPrices: string[];
  stableCoins: string[];
  handleAssetPress: (asset: AuthAsset) => void;
  handleStableCoinPress: (asset: AuthAsset, stableCoin: string) => void;
  blur: boolean;
  currency: string;
  index: number;
};

const WalletAsset = ({
  asset,
  calculatedPrices,
  tokenPrices,
  stableCoins,
  handleAssetPress,
  handleStableCoinPress,
  blur,
  currency,
  index,
}: WalletAssetProps) => {
  const viewFocused = useIsFocused();
  const [expanded, setExpanded] = useState<boolean>(false);

  const [constCalculatedPrices, setConstCalculatedPrices] =
    useState<string[]>(calculatedPrices);
  const [combinedCalcPrice, setCombinedCalcPrice] = useState<string>('0.00');
  const [constTokenPrices, setConstTokenPrices] = useState<string[]>(tokenPrices);
  const [constStableCoins, setConstStableCoins] = useState<string[]>(stableCoins);

  useEffect(() => {
    if (!viewFocused) return;

    setConstCalculatedPrices(calculatedPrices);
    setConstTokenPrices(tokenPrices);
    setConstStableCoins(stableCoins);
    let calcPrice = 0;
    if (calculatedPrices) {
      for (let i = 0; i < calculatedPrices.length; i++) {
        if (calculatedPrices[i]) {
          calcPrice += parseFloat(calculatedPrices[i] || '0');
        }
      }
    }
    setCombinedCalcPrice(calcPrice.toFixed(2));
  }, [viewFocused, calculatedPrices, tokenPrices, stableCoins]);

  return (
    <View style={styles.container} key={index}>
      <TouchableOpacity
        key={asset.title}
        style={styles.assetContainer}
        onPress={() => {
          handleAssetPress(asset);
        }}
      >
        <View style={styles.leftContainer}>
          <TouchableOpacity
            style={styles.tokenLogoContainer}
            onPress={() => {
              toggleStableCoins(setExpanded, expanded);
            }}
            disabled={!StableChains.includes(asset.title)}
          >
            <Logo
              name={symbolToSvg(asset.tokenSymbol === 'BSC' ? 'BNB' : asset.tokenSymbol)}
            />

            {!expanded &&
              StableChains.includes(asset.title) &&
              ChainToTokens[asset.title]?.map((token, index) => {
                const offset =
                  asset.title === 'solana'
                    ? (index + 1) * 2.5 // Positive offset for Solana (shadows below)
                    : index * 2.5 - 7.5; // Original calculation for other assets
                const opacity = 0.6 - index * 0.2;
                const zIndex = -(index + 1);
                return (
                  <View
                    key={`${asset.title}-${token.tokenSymbol}-${index}`}
                    style={{
                      position: 'absolute',
                      height: '100%',
                      width: '100%',
                      zIndex: zIndex,
                      top: offset,
                      opacity: opacity,
                    }}
                  >
                    <Logo
                      name={symbolToSvg(
                        asset.tokenSymbol === 'BSC' ? 'BNB' : asset.tokenSymbol,
                      )}
                    />
                  </View>
                );
              })}
          </TouchableOpacity>
          <View style={styles.tokenInfoContainer}>
            <FixedText style={styles.tokenName}>{NameParsing(asset.title)}</FixedText>
            <FixedText style={styles.tokenPrice}>
              {currency === 'EUR' ? '€' : '$'}
              {constTokenPrices ? parsePrice(constTokenPrices[0] || '0.00') : '0.00'}
            </FixedText>
          </View>
        </View>
        <BlurView isBlurred={blur}>
          <View style={styles.rightContainer}>
            <FixedText style={styles.tokenAmount}>
              {parsePrice(asset.amount)}{' '}
              {asset.tokenSymbol === 'BSC' ? 'BNB' : asset.tokenSymbol}
            </FixedText>
            <FixedText style={styles.calculatedPrice}>
              {currency === 'EUR' ? '€' : '$'}
              {!expanded && StableChains.includes(asset.title) && constCalculatedPrices
                ? parsePrice(parseFloat(combinedCalcPrice).toFixed(2))
                : constCalculatedPrices != undefined
                ? parsePrice(parseFloat(constCalculatedPrices[0] || '0').toFixed(2))
                : '0.00'}
            </FixedText>
          </View>
        </BlurView>
      </TouchableOpacity>
      {expanded && StableChains.includes(asset.title) && (
        <View style={{overflow: 'hidden'}}>
          <View
            style={{
              ...styles.verticalLine,
              ...{height: 51 + ((ChainToTokens[asset.title]?.length || 0) - 1) * 69},
            }}
          />

          {ChainToTokens[asset.title]?.map((token, index) => {
            let amount = constStableCoins
              ? constStableCoins[index]
                ? constStableCoins[index]
                : '0.00'
              : '0.00';

            return (
              <View key={`${asset.title}-${token.tokenSymbol}-${index}`}>
                <View style={styles.shortLine} />
                <View style={{marginLeft: 50}}>
                  <View style={styles.horizontalLine} />

                  <TouchableOpacity
                    style={styles.assetContainer}
                    onPress={() => handleStableCoinPress(asset, token.tokenSymbol)}
                  >
                    <View style={styles.leftContainer}>
                      <View style={styles.stableCoinLogoContainer}>
                        <Logo
                          name={symbolToSvg(
                            token.tokenSymbol +
                              (asset.tokenSymbol === 'BSC' ? 'BNB' : asset.tokenSymbol),
                          )}
                        />
                      </View>
                      <View style={styles.tokenInfoContainer}>
                        <FixedText style={styles.tokenName}>
                          {NameParsing(token.tokenSymbol)}
                        </FixedText>
                        <FixedText style={styles.tokenPrice}>{token.tokenName}</FixedText>
                      </View>
                    </View>
                    <BlurView isBlurred={blur}>
                      <View style={styles.rightContainer}>
                        <FixedText style={styles.tokenAmount}>
                          {amount} {token.tokenSymbol}
                        </FixedText>
                        <FixedText style={styles.calculatedPrice}>
                          {currency === 'EUR' ? '€' : '$'}
                          {constCalculatedPrices
                            ? constCalculatedPrices[index + 1] != undefined
                              ? parseFloat(
                                  constCalculatedPrices[index + 1] || '0',
                                ).toFixed(2)
                              : '0.00'
                            : '0.00'}
                        </FixedText>
                      </View>
                    </BlurView>
                  </TouchableOpacity>
                </View>
              </View>
            );
          })}
        </View>
      )}
    </View>
  );
};

export default WalletAsset;
