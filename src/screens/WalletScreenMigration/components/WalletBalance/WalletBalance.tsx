import {useTranslation} from 'react-i18next';
import {Text, View} from 'react-native';

import BlurView from '@/components/BlurView';
import {styles} from './styles';

type WalletBalanceProps = {
  balance: string;
  blur: boolean;
  currency: string;
};

const WalletBalance = ({balance, blur, currency}: WalletBalanceProps) => {
  const {t} = useTranslation();

  let balanceBeforeDecimal = balance.split('.')[0];
  // Split the price before the decimal point into groups of 3 digits
  balanceBeforeDecimal = balanceBeforeDecimal.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Get the balance after the decimal point and slice it to 2 digits
  let balanceAfterDecimal = balance.split('.')[1]
    ? balance.split('.')[1].slice(0, 2)
    : '00';

  return (
    <View style={styles.topContainer}>
      <BlurView isBlurred={blur}>
        <Text style={styles.balanceText}>{t('balance')}</Text>
        <View style={styles.balanceContainer}>
          <Text style={styles.dollarSign}>{currency === 'EUR' ? '€' : '$'}</Text>
          <View style={styles.balanceContainer}>
            <Text style={styles.balanceValue}>{balanceBeforeDecimal} </Text>
            <Text style={styles.balanceValueAfterDecimal}> .{balanceAfterDecimal}</Text>
          </View>
        </View>
      </BlurView>
    </View>
  );
};

export default WalletBalance;
