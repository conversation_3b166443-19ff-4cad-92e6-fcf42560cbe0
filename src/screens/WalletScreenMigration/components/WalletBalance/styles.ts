import {StyleSheet} from 'react-native';
import GlobalStyles from '@/constants/GlobalStyles';

export const styles = StyleSheet.create({
  topContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    marginTop: 10,
  },
  balanceContainer: {
    alignItems: 'flex-start',
    justifyContent: 'center',
    flexDirection: 'row',
    marginRight: 3.5,
  },
  balanceText: {
    fontSize: 22,
    lineHeight: 25,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    textAlign: 'center',
    color: GlobalStyles.gray.gray800,
    fontStyle: 'normal',
    marginLeft: -6,
  },
  balanceValue: {
    color: GlobalStyles.primary.primary500,
    textAlign: 'center',
    textAlignVertical: 'center',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 39,
    fontWeight: '600',
    lineHeight: 60,
    marginRight: -6,
    marginLeft: 5,
  },
  balanceValueAfterDecimal: {
    color: GlobalStyles.primary.primary300,
    textAlign: 'center',
    textAlignVertical: 'center',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 50,
    marginRight: 5,
  },
  dollarSign: {
    fontSize: 18,
    lineHeight: 50,
    fontWeight: '600',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.primary.primary500,
    fontStyle: 'normal',
    textAlign: 'center',
    textAlignVertical: 'center',
    marginLeft: 5,
  },
});
