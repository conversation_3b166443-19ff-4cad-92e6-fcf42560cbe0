import analytics from '@react-native-firebase/analytics';
import {useFocusEffect} from '@react-navigation/native';
import i18next from 'i18next';
import React, {memo, useCallback, useEffect, useState} from 'react';
import {initReactI18next, useTranslation} from 'react-i18next';
import {
  Dimensions,
  RefreshControl,
  ScrollView,
  StyleSheet,
  View,
  TouchableOpacity,
} from 'react-native';
import FixedText from '@/components/FixedText/FixedText';

import {ChainToTokens} from '@/constants/Chains';
import GlobalStyles from '@/constants/GlobalStyles';
import {languageResources} from '@/constants/i18next';
import {useDebounce} from '@/hooks';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {useAppDispatch, useAuth, useCommon} from '@/hooks/redux';
import {navigate} from '@/navigation/utils/navigation';
import {
  setAvalancheFixed,
  setUser,
  setUserAddresses,
} from '@/storage/actions/authActions';
import CarouselParallax from '@components/CarouselParallax';
import LoadingHandler from '@components/LoadingHandler';
import SafeAreaInset from '@components/SafeAreaInset';
import SendReceiveButtons from '@components/SendReceiveButtons/SendReceiveButtons';
import theme from '@styles/theme';

// Migration components
import AssetList from './components/AssetList/AssetList';
import TopNavigation from './components/TopNavigation/TopNavigation';
import WalletBalance from './components/WalletBalance/WalletBalance';

// Migration hooks
import {useSubscribedChains} from './hooks/useSubscribedChains';
import {useBlockbookWalletData} from './hooks/useBlockbookWalletData';

// Original utils (keeping for compatibility)
import {
  checkNotifSubscription,
  checkSolanaWallet,
  checkWallets,
} from '../WalletScreen/walletScreenUtils';
import {useSecondaryAssets} from '../WalletScreen/hooks/useSecondaryAssets';

const WalletScreenMigration = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {open: openSheet, close: closeSheet} = useBottomSheet('wallet');

  const {userAddresses, user, avalancheFixed} = useAuth();

  const {currency, language} = useCommon();

  // New blockbook-based hooks
  const subscribedChains = useSubscribedChains();
  console.log('subscribedChains', subscribedChains);
  const walletData = useBlockbookWalletData(subscribedChains.subscribedChains, currency);

  // Keep secondary assets for compatibility
  const secondaryAssets = useSecondaryAssets(user, userAddresses);

  const debouncedLoading = useDebounce(
    walletData.loading || secondaryAssets.loading || subscribedChains.loading,
    500,
  );

  const [blur, setBlur] = useState(false);

  const handleMenuPress = useCallback(() => {
    analytics().logEvent('Home_infoButton_tap');
    navigate('Info');
  }, []);

  const handleSettingsPress = useCallback(() => {
    analytics().logEvent('Home_settingsButton_tap');
    navigate('Settings');
  }, []);

  const handleNotificationsPress = useCallback(() => {
    navigate('Notifications');
  }, []);

  const handleSendPress = useCallback(() => {
    analytics().logEvent('Home_sendButton_tap');
    navigate('BottomTabs', {
      screen: 'Wallet',
      params: {
        screen: 'SendAssetInput',
        params: {
          assets: walletData.assets,
          tokenPrices: walletData.tokenPrices,
          calculatedPrices: walletData.calculatedPrices,
          stableCoins: walletData.stableCoins,
        },
      },
    });
  }, [
    walletData.assets,
    walletData.tokenPrices,
    walletData.calculatedPrices,
    walletData.stableCoins,
  ]);

  const handleReceivePress = useCallback(() => {
    analytics().logEvent('Home_receiveButton_tap');

    navigate('BottomTabs', {
      screen: 'Wallet',
      params: {
        screen: 'ReceiveAsset',
        params: {
          assets: walletData.assets,
          tokenPrices: walletData.tokenPrices,
        },
      },
    });
  }, [walletData.assets, walletData.tokenPrices]);

  const handleAssetPress = useCallback(
    (asset: any) => {
      console.log('asset', asset);

      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.blockchain);

      const addressFix =
        asset.blockchain === 'kaspa'
          ? userAddresses[index]?.addresses[0]
          : userAddresses[index]?.address.includes('bitcoincash:')
          ? userAddresses[index]?.address.split('bitcoincash:')[1]
          : userAddresses[index]?.address;

      console.log('INDEX >>>>', index);
      console.log('addresses', addressFix);
      if (!addressFix) return;

      navigate('CurrencySpecific', {
        asset,
        stableCoin: '',
        stableCoinIndex: 0,
        stableCoinAmount: '0.00',
        address: addressFix,
        calculatedPrices: walletData.calculatedPrices[index] || [],
        tokenPrices: walletData.tokenPrices[index] || [],
      });
    },
    [
      walletData.assets,
      walletData.tokenPrices,
      walletData.calculatedPrices,
      user.wallet,
      userAddresses,
    ],
  );

  const handleStableCoinPress = useCallback(
    (asset: any, stableCoin: string) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.title);
      const stableCoinIndex =
        ChainToTokens[asset.title]?.findIndex((t) => t.tokenSymbol === stableCoin) + 1;

      if (!stableCoinIndex) return;

      const amount = walletData.stableCoins[index]?.[stableCoinIndex - 1] ?? '0';
      console.log('amount', amount);
      if (
        !amount ||
        !walletData.calculatedPrices[index] ||
        !walletData.tokenPrices[index]
      ) {
        return;
      }

      navigate('CurrencySpecific', {
        asset,
        stableCoin,
        stableCoinIndex,
        stableCoinAmount: amount,
        address: userAddresses[index],
        calculatedPrices: walletData.calculatedPrices[index] || [],
        tokenPrices: walletData.tokenPrices[index] || [],
      });
    },
    [
      walletData.stableCoins,
      walletData.calculatedPrices,
      walletData.tokenPrices,
      user.wallet,
      userAddresses,
    ],
  );

  const handleBuyPress = useCallback(() => {
    analytics().logEvent('Home_buyButton_tap');
    openSheet({
      type: 'wallet',
      assets: walletData.assets,
      calculatedPrices: walletData.calculatedPrices,
      tokenPrices: walletData.tokenPrices,
      stableCoins: walletData.stableCoins,
      handleAssetPress: (asset) => {
        handleRampAssetPress(asset);
        closeSheet();
      },
      handleStableCoinPress: (asset, stableCoin) => {
        handleRampStableCoinPress(asset, stableCoin);
        closeSheet();
      },
      blur,
      setBlur,
      currency,
    });
  }, [
    walletData.assets,
    walletData.tokenPrices,
    walletData.calculatedPrices,
    walletData.stableCoins,
    blur,
    currency,
  ]);

  const handleRampAssetPress = useCallback(
    (asset: any) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.blockchain);
      navigate('RampDetails', {
        currency: asset.title,
        currencySymbol: asset.tokenSymbol,
        tokenPrice: walletData.tokenPrices[index]?.[0] || [],
        addressToUse: userAddresses[index]?.address,
      });
    },
    [walletData.tokenPrices, userAddresses, user.wallet],
  );

  const handleRampStableCoinPress = useCallback(
    (asset: any, stableCoin: string) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.title);
      const normalizedStableCoin = stableCoin === 'USDT20' ? 'USDT' : stableCoin;
      const stableCoinIndex =
        ChainToTokens[asset.title]?.findIndex(
          (t) => t.tokenSymbol === normalizedStableCoin,
        ) + 1;

      if (!stableCoinIndex) return;

      navigate('RampDetails', {
        currency: normalizedStableCoin,
        currencySymbol: normalizedStableCoin,
        tokenPrice: walletData.tokenPrices[index]?.[stableCoinIndex],
        addressToUse: userAddresses[index]?.address,
        blockchainSymbol: asset.tokenSymbol,
      });
    },
    [walletData.tokenPrices, userAddresses, user.wallet],
  );

  useFocusEffect(
    useCallback(() => {
      const initializeWallet = async () => {
        if (user !== null && !avalancheFixed) {
          const check = await checkWallets(user.wallet, userAddresses);
          if (check !== null) {
            dispatch(setUser({...user, wallet: check.wallets}));
            dispatch(setUserAddresses(check.addresses));
            dispatch(setAvalancheFixed());
            walletData.refreshData();
          } else if (!avalancheFixed) {
            dispatch(setAvalancheFixed());
          }
        }

        analytics().logEvent('Home_screen_open');
        checkNotifSubscription(userAddresses[0]?.address);

        // Refresh both subscribed chains and wallet data
        subscribedChains.refreshSubscribedChains();
        walletData.refreshData();

        i18next.use(initReactI18next).init({
          compatibilityJSON: 'v3',
          lng: language,
          fallbackLng: 'en',
          resources: languageResources,
        });

        const availableAssets = await secondaryAssets.getAvailableSecondaryAssets();
        if (availableAssets.length > 0) {
          const success = await secondaryAssets.createSecondaryWallets(availableAssets);
          if (success) {
            subscribedChains.refreshSubscribedChains();
            walletData.refreshData();
          }
        }

        const result = await checkSolanaWallet(user.wallet, userAddresses);
        if (result !== null) {
          dispatch(setUserAddresses(result.addresses));
          dispatch(setUser({...user, wallet: result.wallets}));
        }
      };

      initializeWallet();
    }, [user, userAddresses, avalancheFixed, language]),
  );

  useEffect(() => {
    walletData.setLoading(true);
  }, [currency]);

  // Show loading if any of the main data sources are loading
  if (debouncedLoading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <LoadingHandler />
      </View>
    );
  }

  // Show error state if subscribed chains failed to load
  if (subscribedChains.error) {
    return (
      <View
        style={{flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20}}
      >
        <FixedText style={{textAlign: 'center', color: 'red', marginBottom: 20}}>
          Error loading subscribed chains: {subscribedChains.error}
        </FixedText>
        <TouchableOpacity
          onPress={subscribedChains.refreshSubscribedChains}
          style={{
            backgroundColor: GlobalStyles.primary.primary500,
            padding: 10,
            borderRadius: 8,
          }}
        >
          <FixedText style={{color: 'white'}}>Retry</FixedText>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <SafeAreaInset type="top" />

      <TopNavigation
        handleMenu={handleMenuPress}
        handleNotifications={handleNotificationsPress}
        handleSettings={handleSettingsPress}
        title={t('wallet.title')}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={walletData.refreshing}
            onRefresh={walletData.onRefresh}
            tintColor={GlobalStyles.primary.primary500}
            colors={[GlobalStyles.primary.primary500]}
          />
        }
      >
        <View style={styles.carouselContainer}>
          <CarouselParallax
            images={[
              {
                link: 'Subscribe',
                title: 'Need a Loan?',
                subtitle: 'Get a crypto-backed fiat loan in 10 minutes',
              },
              {
                link: 'Subscribe',
                title: 'Your Crypto, Simplified',
                subtitle: 'Secure, flexible, and tailored to you',
              },
            ]}
            height={200}
          />
        </View>

        <View style={styles.balance}>
          <WalletBalance balance={walletData.balance} blur={blur} currency={currency} />

          <SendReceiveButtons
            handleSendPress={handleSendPress}
            handleReceivePress={handleReceivePress}
            handleBuyPress={handleBuyPress}
            buyButtonTitle="Buy / Sell"
          />
        </View>

        <View style={styles.assetsContainer}>
          <AssetList
            assets={walletData.assets}
            blur={blur}
            setBlur={setBlur}
            tokenPrices={walletData.tokenPrices}
            handleAssetPress={handleAssetPress}
            handleStableCoinPress={handleStableCoinPress}
            currency={currency}
          />
        </View>
      </ScrollView>
    </>
  );
};

export default memo(WalletScreenMigration);

const styles = StyleSheet.create({
  assetsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    display: 'flex',
    alignSelf: 'center',
    width: '90%',
    paddingBottom: theme.spacing.lg,
  },
  balance: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 10,
    overflow: 'hidden',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    display: 'flex',
    width: '90%',
    elevation: 4,
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  carouselContainer: {
    marginBottom: 18,
    width: '100%',
    paddingHorizontal: 0,
    alignItems: 'center',
  },
});
