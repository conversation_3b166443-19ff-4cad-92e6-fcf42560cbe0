import {useCallback, useEffect, useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {PriceFetchingInstance} from '@/services/BackendServices';
import {getBalances} from '@/utils/blockbook/blockbook-actions';
import {
  setReduxAssets,
  setReduxCalculatedPrices,
  setReduxStableCoins,
  setReduxTokenPrices,
  setWalletBalance,
} from '@/storage/actions/authActions';
import {AuthAsset} from '@/types/authTypes';
import {
  transformBlockbookData,
  mergeAssetsAndTokens,
} from '../utils/blockbookDataTransformers';
import {
  BlockbookBalancesResponse,
  PriceData,
  SubscribedChain,
  TransformedAsset,
  TransformedToken,
  WalletDataState,
} from '../types/blockbookTypes';

export const useBlockbookWalletData = (
  subscribedChains: SubscribedChain[],
  currency: string = 'USD',
) => {
  const dispatch = useDispatch();
  const refreshTimeoutRef = useRef<NodeJS.Timeout>();

  const [state, setState] = useState<WalletDataState>({
    loading: true,
    refreshing: false,
    assets: [],
    tokens: [],
    balance: '0.00',
    subscribedChains: [],
    error: null,
  });

  /**
   * Fetches price data for all relevant symbols
   */
  const fetchPriceData = useCallback(async (symbols: string[]): Promise<PriceData> => {
    const priceData: PriceData = {};

    await Promise.all(
      symbols.map(async (symbol) => {
        try {
          const response = await PriceFetchingInstance.get(`/price/${symbol}`);
          priceData[symbol] = response.data.price.price;
        } catch (error) {
          console.error(`Error fetching price for ${symbol}:`, error);
          priceData[symbol] = '0';
        }
      }),
    );

    return priceData;
  }, []);

  /**
   * Converts transformed assets to legacy AuthAsset format for Redux compatibility
   */
  const convertToAuthAssets = useCallback((assets: TransformedAsset[]): AuthAsset[] => {
    return assets.map((asset) => ({
      title: asset.title,
      amount: asset.amount,
      tokenLogo: asset.tokenLogo,
      tokenSymbol: asset.tokenSymbol,
      blockchain: asset.blockchain,
      network: asset.network,
    }));
  }, []);

  /**
   * Converts price and calculation data to legacy Redux format
   */
  const convertToLegacyFormat = useCallback(
    (assets: TransformedAsset[], tokens: TransformedToken[]) => {
      const calculatedPrices: string[][] = [];
      const tokenPrices: string[][] = [];
      const stableCoins: string[][] = [];

      assets.forEach((asset, index) => {
        // Main asset price
        calculatedPrices[index] = [asset.calculatedValue || '0'];
        tokenPrices[index] = [asset.fiatPrice || '0'];

        // Tokens for this asset
        const assetTokens = tokens.filter(
          (token) => token.blockchain === asset.blockchain,
        );
        assetTokens.forEach((token) => {
          calculatedPrices[index].push(token.calculatedValue || '0');
          tokenPrices[index].push(token.fiatPrice || '0');
        });

        // Stable coins (tokens) for stable chains
        if (
          asset.blockchain === 'ethereum' ||
          asset.blockchain === 'binance-smart-chain' ||
          asset.blockchain === 'avalanche' ||
          asset.blockchain === 'trx' ||
          asset.blockchain === 'solana'
        ) {
          stableCoins[index] = assetTokens.map((token) => token.amount);
        } else {
          stableCoins[index] = [];
        }
      });

      return {calculatedPrices, tokenPrices, stableCoins};
    },
    [],
  );

  /**
   * Main data fetching function
   */
  const fetchWalletData = useCallback(async () => {
    try {
      setState((prev) => ({...prev, error: null}));

      if (subscribedChains.length === 0) {
        setState((prev) => ({
          ...prev,
          loading: false,
          assets: [],
          tokens: [],
          balance: '0.00',
        }));
        return;
      }

      // Fetch balances from blockbook
      const balancesResponse: BlockbookBalancesResponse = await getBalances();

      // Collect all symbols that need price data
      const symbols = new Set<string>();
      subscribedChains.forEach((chain) => symbols.add(chain.chainSymbol));

      // Add token symbols from balances
      balancesResponse.balances.forEach((balance) => {
        if (balance.tokens) {
          balance.tokens.forEach((token) => symbols.add(token.symbol));
        }
      });

      // Fetch price data
      const priceData = await fetchPriceData(Array.from(symbols));

      // Transform blockbook data
      const {assets, tokens, totalBalance} = transformBlockbookData(
        balancesResponse,
        subscribedChains,
        priceData,
        currency,
      );

      // Convert to legacy format for Redux
      const authAssets = convertToAuthAssets(assets);
      const {calculatedPrices, tokenPrices, stableCoins} = convertToLegacyFormat(
        assets,
        tokens,
      );

      // Update Redux state
      dispatch(setReduxAssets(authAssets));
      dispatch(setReduxCalculatedPrices(calculatedPrices));
      dispatch(setReduxTokenPrices(tokenPrices));
      dispatch(setReduxStableCoins(stableCoins));
      dispatch(setWalletBalance(totalBalance));

      // Update local state
      setState((prev) => ({
        ...prev,
        assets,
        tokens,
        balance: totalBalance,
        subscribedChains,
        loading: false,
      }));
    } catch (error) {
      console.error('Error fetching wallet data:', error);
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch wallet data',
        loading: false,
      }));
    }
  }, [
    subscribedChains,
    currency,
    fetchPriceData,
    convertToAuthAssets,
    convertToLegacyFormat,
    dispatch,
  ]);

  /**
   * Refresh function with timeout
   */
  const onRefresh = useCallback(async () => {
    setState((prev) => ({...prev, refreshing: true}));

    // Set a timeout to hide the refresh indicator
    refreshTimeoutRef.current = setTimeout(() => {
      setState((prev) => ({...prev, refreshing: false}));
    }, 2000);

    try {
      await fetchWalletData();
    } catch (error) {
      console.error('Error in refresh:', error);
    } finally {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
      setState((prev) => ({...prev, refreshing: false}));
    }
  }, [fetchWalletData]);

  /**
   * Manual refresh function
   */
  const refreshData = useCallback(() => {
    setState((prev) => ({...prev, loading: true}));
    fetchWalletData();
  }, [fetchWalletData]);

  /**
   * Set loading state
   */
  const setLoading = useCallback((loading: boolean) => {
    setState((prev) => ({...prev, loading}));
  }, []);

  // Fetch data when subscribed chains change
  useEffect(() => {
    if (subscribedChains.length > 0) {
      fetchWalletData();
    }
  }, [fetchWalletData, subscribedChains]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  // Get Redux data for compatibility
  const reduxCalculatedPrices = useSelector((state: any) => state.auth.calculatedPrices);
  const reduxTokenPrices = useSelector((state: any) => state.auth.tokenPrices);
  const reduxStableCoins = useSelector((state: any) => state.auth.stableCoins);

  return {
    loading: state.loading,
    refreshing: state.refreshing,
    assets: mergeAssetsAndTokens(state.assets, state.tokens), // Merge for UI compatibility
    calculatedPrices: reduxCalculatedPrices || [],
    stableCoins: reduxStableCoins || [],
    tokenPrices: reduxTokenPrices || [],
    balance: state.balance,
    error: state.error,
    onRefresh,
    refreshData,
    setLoading,
  };
};
