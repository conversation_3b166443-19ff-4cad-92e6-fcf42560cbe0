import {useCallback, useEffect, useState} from 'react';
import {useAuth} from '@/hooks/redux';
import {getBlockbookHealthStatus} from '@/utils/blockbook/blockbook-actions';
import {getSubscribedChains} from '../utils/blockbookDataTransformers';
import {BlockbookHealthResponse, SubscribedChain} from '../types/blockbookTypes';

export const useSubscribedChains = () => {
  const {userAddresses} = useAuth();
  const [subscribedChains, setSubscribedChains] = useState<SubscribedChain[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscribedChains = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get user's primary address
      const userAddress = userAddresses[0]?.address;
      if (!userAddress) {
        throw new Error('No user address found');
      }

      // Fetch blockbook health status
      const healthResponse: BlockbookHealthResponse = await getBlockbookHealthStatus();

      // Transform health status to subscribed chains
      const chains = getSubscribedChains(healthResponse, userAddress);

      setSubscribedChains(chains);
    } catch (err) {
      console.error('Error fetching subscribed chains:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch subscribed chains');
      setSubscribedChains([]);
    } finally {
      setLoading(false);
    }
  }, [userAddresses]);

  const refreshSubscribedChains = useCallback(() => {
    fetchSubscribedChains();
  }, [fetchSubscribedChains]);

  useEffect(() => {
    if (userAddresses.length > 0) {
      fetchSubscribedChains();
    }
  }, [fetchSubscribedChains, userAddresses]);

  return {
    subscribedChains,
    loading,
    error,
    refreshSubscribedChains,
  };
};
