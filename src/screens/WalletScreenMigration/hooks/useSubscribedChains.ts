import {useCallback, useEffect, useState} from 'react';
import {useAuth} from '@/hooks/redux';
import {getBlockbookHealthStatus} from '@/utils/blockbook/blockbook-actions';
import {getSubscribedChains} from '../utils/blockbookDataTransformers';
import {BlockbookHealthResponse, SubscribedChain} from '../types/blockbookTypes';

export const useSubscribedChains = () => {
  const {userAddresses} = useAuth();
  const [subscribedChains, setSubscribedChains] = useState<SubscribedChain[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscribedChains = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get user's primary address
      const userAddress = userAddresses[0]?.address;
      if (!userAddress) {
        console.log('🔴 No user address found');
        setSubscribedChains([]);
        setLoading(false);
        return;
      }

      console.log('🔵 Fetching blockbook health status for address:', userAddress);

      // Fetch blockbook health status
      const healthResponse: BlockbookHealthResponse = await getBlockbookHealthStatus();

      console.log('🔵 Health response:', healthResponse);

      // Transform health status to subscribed chains
      const chains = getSubscribedChains(healthResponse, userAddress);

      console.log('🔵 Subscribed chains found:', chains);

      setSubscribedChains(chains);
    } catch (err) {
      console.error('🔴 Error fetching subscribed chains:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch subscribed chains');
      setSubscribedChains([]);
    } finally {
      setLoading(false);
    }
  }, [userAddresses]);

  const refreshSubscribedChains = useCallback(() => {
    fetchSubscribedChains();
  }, [fetchSubscribedChains]);

  useEffect(() => {
    console.log('🔵 useSubscribedChains: userAddresses changed:', userAddresses);
    if (userAddresses.length > 0) {
      fetchSubscribedChains();
    } else {
      console.log('🟡 No user addresses, setting loading to false');
      setLoading(false);
      setSubscribedChains([]);
    }
  }, [fetchSubscribedChains, userAddresses]);

  return {
    subscribedChains,
    loading,
    error,
    refreshSubscribedChains,
  };
};
