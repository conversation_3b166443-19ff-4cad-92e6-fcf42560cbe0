// Blockbook Health Status Types
export interface BlockbookChainStatus {
  connection: 'connected' | 'disconnected';
  subscribedAddresses: number;
  addresses: string[];
}

export interface BlockbookHealthResponse {
  status: {
    [chainSymbol: string]: BlockbookChainStatus;
  };
}

// Blockbook Balance Types
export interface BlockbookToken {
  type: string;
  standard?: string;
  name: string;
  contract: string;
  transfers: number;
  symbol: string;
  decimals: number;
  balance?: string;
}

export interface BlockbookChainBalance {
  chain: string;
  address: string;
  balance: string;
  unconfirmedBalance: string;
  tokens?: BlockbookToken[];
  // UTXO specific fields
  totalReceived?: string;
  totalSent?: string;
}

export interface BlockbookBalancesResponse {
  balances: BlockbookChainBalance[];
}

// Transformed Types for UI
export interface TransformedAsset {
  title: string;
  amount: string;
  tokenLogo: string;
  tokenSymbol: string;
  blockchain: string;
  network: string;
  rawBalance: string;
  decimals: number;
  fiatPrice?: string;
  calculatedValue?: string;
}

export interface TransformedToken extends TransformedAsset {
  contractAddress: string;
  tokenName: string;
  isToken: true;
}

export interface SubscribedChain {
  chainSymbol: string;
  chainName: string;
  isSubscribed: boolean;
  isConnected: boolean;
  userAddress: string;
}

// Chain mapping types
export interface ChainConfig {
  symbol: string;
  name: string;
  decimals: number;
  isStableChain: boolean;
}

export const CHAIN_CONFIGS: Record<string, ChainConfig> = {
  BTC: { symbol: 'BTC', name: 'bitcoin', decimals: 8, isStableChain: false },
  BCH: { symbol: 'BCH', name: 'bitcoin-cash', decimals: 8, isStableChain: false },
  LTC: { symbol: 'LTC', name: 'litecoin', decimals: 8, isStableChain: false },
  DOGE: { symbol: 'DOGE', name: 'dogecoin', decimals: 8, isStableChain: false },
  ETH: { symbol: 'ETH', name: 'ethereum', decimals: 18, isStableChain: true },
  BSC: { symbol: 'BNB', name: 'binance-smart-chain', decimals: 18, isStableChain: true },
  AVAX: { symbol: 'AVAX', name: 'avalanche', decimals: 18, isStableChain: true },
  SOL: { symbol: 'SOL', name: 'solana', decimals: 9, isStableChain: true },
  TRX: { symbol: 'TRX', name: 'trx', decimals: 6, isStableChain: true },
  XRP: { symbol: 'XRP', name: 'xrp', decimals: 6, isStableChain: false },
  KAS: { symbol: 'KAS', name: 'kaspa', decimals: 8, isStableChain: false },
};

// Price fetching types
export interface PriceData {
  [symbol: string]: string;
}

export interface WalletDataState {
  loading: boolean;
  refreshing: boolean;
  assets: TransformedAsset[];
  tokens: TransformedToken[];
  balance: string;
  subscribedChains: SubscribedChain[];
  error: string | null;
}
