# WalletScreenMigration

This folder contains the refactored WalletScreen implementation that uses the blockbook proxy instead of the backend API (`WalletLoggerInstance`).

## Key Changes

### 🔄 **Data Source Migration**
- **Before**: Used `WalletLoggerInstance` backend API
- **After**: Uses blockbook proxy via `getBalances()` from `@blockbook-actions.ts`

### 🎯 **Subscription-Based Chain Display**
- Only shows chains that are:
  - Connected (`connection: "connected"`)
  - Subscribed (`subscribedAddresses > 0`)
  - Contains user's primary address (`userAddresses[0].address`)

### 💰 **Balance Transformation**
- Converts raw balances from smallest units (e.g., satoshis for BTC) to human-readable format
- Maintains existing token filtering logic from `ChainToTokens`
- Uses `PriceFetchingInstance` for fiat price conversion

### 🏗️ **Architecture**

```
WalletScreenMigration/
├── WalletScreenMigration.tsx          # Main screen component
├── hooks/
│   ├── useSubscribedChains.ts         # Fetches and manages subscribed chains
│   └── useBlockbookWalletData.ts      # Main data fetching hook
├── components/                        # UI components (copied from original)
│   ├── AssetList/
│   ├── WalletBalance/
│   └── TopNavigation/
├── types/
│   └── blockbookTypes.ts              # TypeScript definitions
└── utils/
    └── blockbookDataTransformers.ts   # Data transformation utilities
```

## Usage

To use the migrated screen, import and use `WalletScreenMigration` instead of `WalletScreen`:

```tsx
import WalletScreenMigration from '@/screens/WalletScreenMigration/WalletScreenMigration';

// Use in navigation or directly
<WalletScreenMigration />
```

## Data Flow

1. **useSubscribedChains**: Calls `getBlockbookHealthStatus()` to determine which chains to show
2. **useBlockbookWalletData**: Calls `getBalances()` to fetch balance data
3. **Data Transformation**: Converts blockbook format to existing `AuthAsset` format
4. **Redux Integration**: Updates Redux state for compatibility with existing components
5. **UI Rendering**: Uses existing UI components with transformed data

## Compatibility

- ✅ Maintains full UI/UX compatibility with original WalletScreen
- ✅ Compatible with existing navigation and component props
- ✅ Preserves Redux state structure for other parts of the app
- ✅ Supports all existing features (send, receive, buy/sell, asset details)

## Error Handling

- Network failures are handled gracefully with retry mechanisms
- Loading states are properly managed
- Error messages are displayed to users with retry options

## Performance

- Efficient data fetching with proper caching
- Debounced loading states to prevent UI flickering
- Optimized re-renders with proper memoization
