import _ from 'lodash';
import {useCallback, useEffect, useRef, useState} from 'react';
import {useDispatch} from 'react-redux';

import {
  ChainsOrder,
  ChainToTokens,
  StableChains,
  SymbolToChain,
} from '@/constants/Chains';
import {PriceFetchingInstance, WalletLoggerInstance} from '@/services/BackendServices';
import {
  setReduxAssets,
  setReduxCalculatedPrices,
  setReduxStableCoins,
  setReduxTokenPrices,
  setWalletBalance,
} from '@/storage/actions/authActions';
import {
  AuthAsset,
  AuthAssets,
  AuthCalculatedPrices,
  AuthStableCoins,
  AuthTokenPrices,
} from '@/types/authTypes';
import {TrailingZeroRegex} from '@/utils/parsing';

export const useWalletData = (
  userAddress: string,
  currency: string,
  initialAssets: AuthAssets,
  initialCalculatedPrices: AuthCalculatedPrices,
  initialStableCoins: AuthStableCoins,
  initialTokenPrices: AuthTokenPrices,
  initialBalance: string,
) => {
  const dispatch = useDispatch();
  const refreshTimeoutRef = useRef<NodeJS.Timeout>();

  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [assets, setAssets] = useState<AuthAssets>(
    initialAssets ? [...initialAssets] : [],
  );
  const [calculatedPrices, setCalculatedPrices] = useState<AuthCalculatedPrices>(
    initialCalculatedPrices ? {...initialCalculatedPrices} : [],
  );
  const [stableCoins, setStableCoins] = useState<AuthStableCoins>(
    initialStableCoins ? {...initialStableCoins} : [],
  );
  const [tokenPrices, setTokenPrices] = useState<AuthTokenPrices>(
    initialTokenPrices ? {...initialTokenPrices} : [],
  );
  const [balance, setBalance] = useState<string>(initialBalance || '0.00');

  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  const refreshData = useCallback(async () => {
    try {
      const res = await WalletLoggerInstance.get(
        `/v1/dashboard-data/wallet-value/${userAddress}${
          currency === 'usd' ? '' : `?currency=${currency}`
        }`,
      );

      const fetchedAssets = res.data;
      if (!fetchedAssets) return;

      let unsortedAssets: AuthAssets = [];
      let unsortedCalculatedPrices: AuthCalculatedPrices = [];
      let unsortedTokenPrices: AuthTokenPrices = [];
      let unsortedStableCoins: AuthStableCoins = [];

      const processAssets = async () => {
        for (const asset of fetchedAssets.chains) {
          let balanceAmount = parseFloat(asset.confirmedBalance.amount);
          let integerPartLength = balanceAmount.toFixed(0).length;
          let decimalPlaces = Math.max(0, 5 - integerPartLength);
          let balance = balanceAmount.toFixed(decimalPlaces);
          balance =
            balance.split('.')[0] +
            '.' +
            (balance.split('.')[1]?.replace(TrailingZeroRegex, '') || '0');

          const chainName = SymbolToChain[asset.chain] || asset.chain;

          const assetData: AuthAsset = {
            title: chainName,
            amount: balance,
            tokenLogo: chainName,
            tokenSymbol: asset.confirmedBalance.unit,
            blockchain: chainName,
            network: 'mainnet',
          };

          unsortedAssets.push(assetData);
          const currentIndex = unsortedAssets.length - 1;

          if (!unsortedCalculatedPrices[currentIndex])
            unsortedCalculatedPrices[currentIndex] = [];
          if (!unsortedStableCoins[currentIndex]) unsortedStableCoins[currentIndex] = [];
          if (!unsortedTokenPrices[currentIndex]) unsortedTokenPrices[currentIndex] = [];

          const priceRes = await PriceFetchingInstance.get(
            `/price/${
              asset.confirmedBalance.unit === 'BSC' ? 'BNB' : asset.confirmedBalance.unit
            }?currency=${currency === 'USD' ? 'USDT' : 'EUR'}`,
          );

          const processTokenPrice = (price: number) => {
            const integerPartLength = price.toFixed(0).length;
            const decimalPlaces = Math.max(0, 5 - integerPartLength);
            const fixed = price.toFixed(decimalPlaces);
            const [before, after] = fixed.split('.');
            const formattedPrice = after
              ? `${before}.${after.replace(TrailingZeroRegex, '')}`
              : before || '0';

            // Remove trailing dot if there's no decimal part
            return formattedPrice.endsWith('.')
              ? formattedPrice.slice(0, -1)
              : formattedPrice;
          };

          const tokenPrice =
            processTokenPrice(parseFloat(priceRes.data.price.price)) || '0.00';
          if (unsortedTokenPrices[currentIndex]) {
            unsortedTokenPrices[currentIndex][0] = tokenPrice;
          }

          const calculatedPrice =
            processTokenPrice(
              parseFloat(asset.confirmedBalance.nativePriceOfAssetInFiat),
            ) || '0.00';
          if (unsortedCalculatedPrices[currentIndex]) {
            unsortedCalculatedPrices[currentIndex][0] = calculatedPrice;
          }

          if (chainName && StableChains.includes(chainName)) {
            // Create an array with the correct size based on ChainToTokens
            if (unsortedStableCoins[currentIndex]) {
              unsortedStableCoins[currentIndex] = new Array(
                ChainToTokens[chainName]?.length || 0,
              ).fill('0.00');
            }

            await Promise.all(
              asset.tokens.map(async (token: any, tokenIndex: number) => {
                let symbol = token.symbol.replace('.e', '').toUpperCase();
                symbol = symbol === 'BSC-USD' ? 'USDT' : symbol;

                const tokenPriceRes = await PriceFetchingInstance.get(
                  `/price/${symbol}?currency=${currency === 'USD' ? 'USDT' : 'EUR'}`,
                );

                const tokenPrice =
                  processTokenPrice(parseFloat(tokenPriceRes.data.price.price)) || '0.00';
                if (unsortedTokenPrices[currentIndex]) {
                  unsortedTokenPrices[currentIndex][tokenIndex + 1] = tokenPrice;
                }

                const calculatedPrice =
                  processTokenPrice(parseFloat(token.priceInFiat)) || '0.00';
                if (unsortedCalculatedPrices[currentIndex]) {
                  unsortedCalculatedPrices[currentIndex][tokenIndex + 1] =
                    calculatedPrice;
                }

                const balance =
                  processTokenPrice(parseFloat(token.confirmedBalance)) || '0.00';
                if (unsortedStableCoins[currentIndex]) {
                  // Find the correct index in ChainToTokens
                  const chainTokens = ChainToTokens[chainName] || [];
                  const matchedIndex = chainTokens.findIndex(
                    (t) => t.tokenSymbol === symbol,
                  );

                  if (matchedIndex !== -1) {
                    unsortedStableCoins[currentIndex][matchedIndex] = balance;
                  } else {
                    // Fallback to original behavior if token not found
                    unsortedStableCoins[currentIndex][tokenIndex] = balance;
                  }
                }
              }),
            );
          }
        }

        // Sort assets according to ChainsOrder
        const updatedAssets: AuthAssets = [];
        const updatedCalculatedPrices: AuthCalculatedPrices = [];
        const updatedStableCoins: AuthStableCoins = [];
        const updatedTokenPrices: AuthTokenPrices = [];

        // First add assets that are in ChainsOrder
        ChainsOrder.forEach((chainName) => {
          const index = unsortedAssets.findIndex(
            (asset) => asset.blockchain === chainName,
          );
          if (index !== -1) {
            const asset = unsortedAssets[index];
            if (asset) {
              updatedAssets.push(asset);
              updatedCalculatedPrices.push(unsortedCalculatedPrices[index] || []);
              updatedStableCoins.push(unsortedStableCoins[index] || []);
              updatedTokenPrices.push(unsortedTokenPrices[index] || []);
            }
          }
        });

        // Then add any remaining assets not in ChainsOrder
        unsortedAssets.forEach((asset, index) => {
          if (!ChainsOrder.includes(asset.blockchain)) {
            updatedAssets.push(asset);
            updatedCalculatedPrices.push(unsortedCalculatedPrices[index] || []);
            updatedStableCoins.push(unsortedStableCoins[index] || []);
            updatedTokenPrices.push(unsortedTokenPrices[index] || []);
          }
        });

        if (fetchedAssets.totalBalanceInFiat !== balance) {
          setBalance(fetchedAssets.totalBalanceInFiat);
          dispatch(setWalletBalance(fetchedAssets.totalBalanceInFiat));
        }

        setAssets(updatedAssets);
        setCalculatedPrices(updatedCalculatedPrices);
        setStableCoins(updatedStableCoins);
        setTokenPrices(updatedTokenPrices);

        // Debounce redux updates
        _.debounce(() => {
          dispatch(setReduxAssets(updatedAssets));
          dispatch(setReduxStableCoins(updatedStableCoins));
          dispatch(setReduxCalculatedPrices(updatedCalculatedPrices));
          dispatch(setReduxTokenPrices(updatedTokenPrices));
        }, 1000)();
      };

      await processAssets();
    } catch (error) {
      console.error('Error refreshing wallet data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [userAddress, currency]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);

    // Set a timeout to hide the refresh indicator after 5 seconds
    refreshTimeoutRef.current = setTimeout(() => {
      setRefreshing(false);
    }, 2000);

    // await WalletLoggerInstance.get(`/v1/wallet/${userAddress}/refresh`);

    refreshData().catch((error) => {
      console.error('Error in background refresh:', error);
    });
  }, [refreshData]);

  return {
    loading,
    refreshing,
    assets,
    calculatedPrices,
    stableCoins,
    tokenPrices,
    balance,
    onRefresh,
    refreshData,
    setLoading,
  };
};
