import {WalletStackParamList} from '@/navigation/types';
import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import {ReactElement} from 'react';

import TopNavigationHeaderExit from '@/components/TopNavigationHeaderExit';
import GlobalStyles from '@/constants/GlobalStyles';
import CurrencySpecificScreen from '@/screens/CurrencySpecificScreen/CurrencySpecificScreen';
import ReceiveAsset from '@/screens/CurrencySpecificScreen/ReceiveAsset/ReceiveAsset';
import SendAssetConfirmation from '@/screens/CurrencySpecificScreen/SendAsset/screens/ConfirmationScreen/SendAssetConfirmation';
import SendAssetInput from '@/screens/CurrencySpecificScreen/SendAsset/screens/SendAssetInput/SendAssetInput';
import RampConfirm from '@/screens/Ramps/RampConfirm';
import RampDetails from '@/screens/Ramps/RampDetails';
import TxDetailsScreen from '@/screens/TxDetailsScreen/TxDetailsScreen';
import SubscribeScreen from '@/screens/WalletScreen/screens/SubscribeScreen/SubscribeScreen';
import WalletScreenMigration from '@/screens/WalletScreenMigration/WalletScreenMigration';
import {DEFAULT_HEADER_STYLE} from '../utils';

const Stack = createStackNavigator<WalletStackParamList>();
const screenOptions: StackNavigationOptions = {
  headerShown: true,
  ...DEFAULT_HEADER_STYLE,
};

const WalletNavigator = (): ReactElement => (
  <Stack.Navigator screenOptions={screenOptions} initialRouteName="WalletHome">
    <Stack.Screen
      name="WalletHome"
      component={WalletScreenMigration}
      options={{headerShown: false}}
    />

    <Stack.Screen
      name="CurrencySpecific"
      component={CurrencySpecificScreen}
      options={{
        headerShown: false,
      }}
    />

    <Stack.Screen
      name="ReceiveAsset"
      component={ReceiveAsset}
      options={{
        headerShown: true,
        title: 'Receive',
      }}
    />

    <Stack.Screen
      name="SendAssetInput"
      component={SendAssetInput}
      options={{
        headerShown: true,
        title: 'Send',
      }}
    />

    <Stack.Screen
      name="SendAssetConfirmation"
      component={SendAssetConfirmation}
      options={{
        headerShown: true,
        title: 'Confirm Send',
      }}
    />

    <Stack.Screen
      name="TxDetails"
      component={TxDetailsScreen}
      options={{
        headerShown: true,
        ...DEFAULT_HEADER_STYLE,
        title: 'Transaction Details',
      }}
    />

    <Stack.Screen
      name="Subscribe"
      component={SubscribeScreen}
      options={{headerShown: false}}
    />

    <Stack.Screen
      name="RampDetails"
      component={RampDetails}
      options={{
        headerShown: true,
        ...DEFAULT_HEADER_STYLE,
        title: 'Ramp',
        headerStyle: {
          ...DEFAULT_HEADER_STYLE.headerStyle,
          backgroundColor: GlobalStyles.base.white,
        },
      }}
    />

    <Stack.Screen
      name="RampConfirm"
      component={RampConfirm}
      options={({navigation}) => ({
        headerShown: true,
        title: 'Choose and Confirm',
        ...DEFAULT_HEADER_STYLE,
        headerRight: () => (
          <TopNavigationHeaderExit
            onPress={() => navigation.replace('BottomTabs', {screen: 'Home'})}
          />
        ),
      })}
    />
  </Stack.Navigator>
);

export default WalletNavigator;
