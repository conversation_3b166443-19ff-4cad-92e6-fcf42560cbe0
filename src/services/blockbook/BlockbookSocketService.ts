import {AppState, AppStateStatus} from 'react-native';
import {io, Socket} from 'socket.io-client';

import {ConnectionState} from './types';

/**
 * `BlockbookSocketService` is a singleton class that manages the socket connection
 */
class BlockbookSocketService {
  private socketRef: Socket | null = null;
  private static instance: BlockbookSocketService | null = null;

  private appStateSubscription: any = null;
  private wasConnectedBeforeBackground: boolean = false;
  private connectionState: ConnectionState = 'disconnected';
  private connectionListeners: Set<(state: ConnectionState) => void> = new Set();

  constructor() {
    this.setupAppStateListener();
  }

  /* ============================================================================================== */
  /*                                         PUBLIC METHODS                                         */
  /* ============================================================================================== */

  public connect(): void {
    if (this.socketRef?.connected || this.connectionState === 'connecting') {
      return;
    }

    try {
      this.setConnectionState('connecting');

      this.socketRef = io('ws://**************:32765', {
        query: {
          v: '1',
          session: 'test-session-123',
          nonce: 'dGVzdA==',
        },
        transports: ['websocket'],
        autoConnect: true,
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to initialize socket connection:', error);
      this.setConnectionState('disconnected');
    }
  }

  public disconnect(): void {
    if (this.socketRef) {
      this.socketRef.disconnect();
      this.socketRef = null;
    }
    this.setConnectionState('disconnected');
  }

  public addConnectionListener(listener: (state: ConnectionState) => void): () => void {
    this.connectionListeners.add(listener);
    return () => this.connectionListeners.delete(listener);
  }

  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  public cleanup(): void {
    this.disconnect();
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
  }

  static getInstance(): BlockbookSocketService {
    if (!BlockbookSocketService.instance) {
      BlockbookSocketService.instance = new BlockbookSocketService();
    }
    return BlockbookSocketService.instance;
  }

  /* ============================================================================================== */
  /*                                         PRIVATE METHODS                                        */
  /* ============================================================================================== */

  private setConnectionState(state: ConnectionState): void {
    this.connectionState = state;
    this.connectionListeners.forEach((listener) => listener(state));
  }

  private setupAppStateListener(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange,
    );
  }

  private handleAppStateChange = (nextAppState: AppStateStatus): void => {
    if (nextAppState === 'background' || nextAppState === 'inactive') {
      if (this.socketRef?.connected) {
        console.log('🔌 App going to background, disconnecting WebSocket');
        this.wasConnectedBeforeBackground = true;
        this.disconnect();
      }
    } else if (nextAppState === 'active') {
      if (this.wasConnectedBeforeBackground && !this.socketRef?.connected) {
        console.log('🔌 App becoming active, reconnecting WebSocket');
        this.wasConnectedBeforeBackground = false;
        this.connect();
      }
    }
  };

  private setupEventListeners(): void {
    if (!this.socketRef) return;

    this.socketRef.on('connect', () => {
      console.log('✅ Socket connected successfully');
      this.setConnectionState('connected');
    });

    this.socketRef.on('disconnect', () => {
      console.log('❌ Socket disconnected');
      this.setConnectionState('disconnected');
    });

    this.socketRef.onAny((eventName, ...args) => {
      console.log(`📧 Received event: ${eventName}`, args);
      // this.socketRef?.emit('ack');
    });
  }
}

export default BlockbookSocketService.getInstance();
