import {
  AUTHENTICATION_SERVICE,
  BLOCKBOOK_PROXY_SERVICE,
  EMAIL_LOGGER_SERVICE,
  NOTIFICATION_SERVICE,
  PRICE_FETCHING_SERVICE,
  RAMPS_SERVICE,
  STAKING_CALCULATOR_BACKEND_SERVICE,
  STATUS_SERVICE,
  SWAPS_SERVICE,
  WALLET_BALANCE_SERVICE,
  WALLET_LOGGER_SERVICE,
} from '@env';
import axios from 'axios';

const WalletLoggerInstance = axios.create({
  baseURL: WALLET_LOGGER_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const StakingCalculatorInstance = axios.create({
  baseURL: STAKING_CALCULATOR_BACKEND_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const WalletBalanceInstance = axios.create({
  baseURL: WALLET_BALANCE_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const PriceFetchingInstance = axios.create({
  baseURL: PRICE_FETCHING_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const EmailLoggerInstance = axios.create({
  baseURL: EMAIL_LOGGER_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const NotificationInstance = axios.create({
  baseURL: NOTIFICATION_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const AuthenticationInstance = axios.create({
  baseURL: AUTHENTICATION_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const RampsInstance = axios.create({
  baseURL: RAMPS_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const ExchangeInstance = axios.create({
  baseURL: SWAPS_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const StatusInstance = axios.create({
  baseURL: STATUS_SERVICE,
  // headers: {
  //   'content-type': 'application/json',
  // },
});

const BlockbookProxyInstance = axios.create({
  baseURL: BLOCKBOOK_PROXY_SERVICE,
  headers: {
    'content-type': 'application/json',
    Authorization: `Session test-session-123`,
  },
});

export {
  AuthenticationInstance,
  BlockbookProxyInstance,
  EmailLoggerInstance,
  ExchangeInstance,
  NotificationInstance,
  PriceFetchingInstance,
  RampsInstance,
  StakingCalculatorInstance,
  StatusInstance,
  WalletBalanceInstance,
  WalletLoggerInstance,
};
